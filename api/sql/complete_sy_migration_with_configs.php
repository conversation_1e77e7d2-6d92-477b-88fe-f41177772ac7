<?php
/**
 * Complete School Year Migration with Master Configs Update
 * 
 * This script handles the complete school year migration process including:
 * - Date validation and timing checks
 * - Outstanding balance migration
 * - Master configs updates (ACTIVE_SY, MOD_ESP)
 * - UI flag management for overlapping periods
 * 
 * Master Configs Logic:
 * - Opening date of next SY: MOD_ESP = 1 (enable overlapping period UI)
 * - Closing date of current SY: MOD_ESP = 0, ACTIVE_SY = nextSY
 * 
 * <AUTHOR> Name
 * @date 2025-01-XX
 */

// Include CakePHP framework
require_once dirname(__FILE__) . '/../config/core.php';
require_once CAKE . 'bootstrap.php';

// Import required models
App::import('Model', 'Account');

/**
 * Complete migration with master configs update
 */
function completeMigrationWithConfigs() {
    echo "=== Complete School Year Migration with Master Configs ===\n\n";
    
    // Initialize Account model
    $Account = new Account();
    
    // Configuration
    $currentSY = 2024;
    $nextSY = 2025;
    
    echo "Configuration:\n";
    echo "- Current SY: {$currentSY}\n";
    echo "- Next SY: {$nextSY}\n";
    echo "- System Date: " . date('Y-m-d H:i:s') . "\n\n";
    
    // Step 1: Show current master configs
    echo "Step 1: Current Master Configs\n";
    echo "------------------------------\n";
    showCurrentConfigs($Account);
    
    // Step 2: Get billing cutoffs and validate timing
    echo "Step 2: Billing Cutoffs and Validation\n";
    echo "--------------------------------------\n";
    $cutoffs = $Account->getBillingCutoffs($currentSY);
    
    if (!$cutoffs) {
        echo "❌ ERROR: No billing cutoffs found for SY {$currentSY}\n";
        return;
    }
    
    displayBillingCutoffs($cutoffs, $currentSY);
    
    $validation = $Account->validateMigrationTiming($currentSY, false);
    displayValidationSummary($validation);
    
    // Step 3: Perform migration with configs update
    echo "Step 3: Performing Complete Migration\n";
    echo "------------------------------------\n";
    
    if (!$validation['allowed']) {
        echo "⚠️  Migration blocked by validation. Use force override? (y/n): ";
        // For demo purposes, we'll proceed with override
        echo "y\n";
        echo "Proceeding with force override...\n\n";
        $forceOverride = true;
    } else {
        $forceOverride = false;
    }
    
    // Perform the migration
    $endBillDate = $cutoffs['end_bill_date'];
    $result = $Account->initializeNextSchoolYear($currentSY, $nextSY, $endBillDate, $forceOverride);
    
    // Step 4: Display migration results
    echo "Step 4: Migration Results\n";
    echo "------------------------\n";
    displayMigrationResults($result);
    
    // Step 5: Show updated master configs
    echo "Step 5: Updated Master Configs\n";
    echo "------------------------------\n";
    showCurrentConfigs($Account);
    
    // Step 6: Verify the changes
    echo "Step 6: Verification\n";
    echo "-------------------\n";
    verifyMigrationAndConfigs($Account, $currentSY, $nextSY);
    
    echo "=== Complete Migration Finished ===\n";
}

/**
 * Show current master configs
 */
function showCurrentConfigs($Account) {
    $activeSY = $Account->getMasterConfig('ACTIVE_SY');
    $modESP = $Account->getMasterConfig('MOD_ESP');
    
    echo "Current Master Configs:\n";
    echo "┌─────────────┬─────────────┬─────────────────────────────────┐\n";
    echo "│ Config Key  │ Value       │ Description                     │\n";
    echo "├─────────────┼─────────────┼─────────────────────────────────┤\n";
    echo "│ ACTIVE_SY   │ " . str_pad($activeSY, 11) . " │ Current active school year      │\n";
    echo "│ MOD_ESP     │ " . str_pad($modESP, 11) . " │ Overlapping period flag         │\n";
    echo "└─────────────┴─────────────┴─────────────────────────────────┘\n";
    
    $modESPStatus = ($modESP == '1') ? '✅ ENABLED (Overlapping period active)' : '❌ DISABLED (Single SY mode)';
    echo "MOD_ESP Status: {$modESPStatus}\n\n";
}

/**
 * Display billing cutoffs
 */
function displayBillingCutoffs($cutoffs, $sy) {
    echo "Billing Cutoffs for SY {$sy}:\n";
    echo "┌─────────────────┬─────────────┬─────────────────────────────────┐\n";
    echo "│ Period          │ Date        │ Master Config Action            │\n";
    echo "├─────────────────┼─────────────┼─────────────────────────────────┤\n";
    echo "│ Opening Date    │ " . str_pad($cutoffs['opening_date'], 11) . " │ MOD_ESP = 1 (enable overlap)   │\n";
    echo "│ Start Bill Date │ " . str_pad($cutoffs['start_bill_date'], 11) . " │ No config change                │\n";
    echo "│ End Bill Date   │ " . str_pad($cutoffs['end_bill_date'], 11) . " │ No config change                │\n";
    echo "│ Closing Date    │ " . str_pad($cutoffs['closing_date'], 11) . " │ MOD_ESP = 0, ACTIVE_SY = next   │\n";
    echo "└─────────────────┴─────────────┴─────────────────────────────────┘\n\n";
}

/**
 * Display validation summary
 */
function displayValidationSummary($validation) {
    $statusIcons = array(
        'ALLOWED' => '✅',
        'CAUTION' => '⚠️ ',
        'WARNING' => '⚠️ ',
        'BLOCKED' => '❌',
        'ERROR' => '❌'
    );
    
    $icon = isset($statusIcons[$validation['status']]) ? $statusIcons[$validation['status']] : '❓';
    
    echo "Migration Validation:\n";
    echo "- Current Period: " . $validation['current_period'] . "\n";
    echo "- Status: {$icon} " . $validation['status'] . "\n";
    echo "- Migration Allowed: " . ($validation['allowed'] ? 'YES' : 'NO') . "\n";
    echo "- Message: " . $validation['message'] . "\n\n";
}

/**
 * Display migration results
 */
function displayMigrationResults($result) {
    $icon = $result['success'] ? '✅' : '❌';
    echo "{$icon} Migration Status: " . ($result['success'] ? 'SUCCESS' : 'FAILED') . "\n";
    echo "Message: " . $result['message'] . "\n\n";
    
    if ($result['success']) {
        echo "Migration Summary:\n";
        echo "- Accounts Updated: " . $result['accounts_updated'] . "\n";
        echo "- Balances Migrated: " . $result['balances_migrated'] . "\n\n";
        
        echo "Steps Completed:\n";
        foreach ($result['steps_completed'] as $step) {
            echo "✓ {$step}\n";
        }
        echo "\n";
    } else {
        echo "Errors:\n";
        foreach ($result['errors'] as $error) {
            echo "✗ {$error}\n";
        }
        echo "\n";
    }
    
    if (!empty($result['warnings'])) {
        echo "Warnings:\n";
        foreach ($result['warnings'] as $warning) {
            echo "⚠️  {$warning}\n";
        }
        echo "\n";
    }
}

/**
 * Verify migration and config changes
 */
function verifyMigrationAndConfigs($Account, $currentSY, $nextSY) {
    // Check master configs
    $newActiveSY = $Account->getMasterConfig('ACTIVE_SY');
    $newModESP = $Account->getMasterConfig('MOD_ESP');
    
    echo "Configuration Verification:\n";
    echo "- ACTIVE_SY should be {$nextSY}: " . ($newActiveSY == $nextSY ? '✅ CORRECT' : '❌ INCORRECT') . " (actual: {$newActiveSY})\n";
    echo "- MOD_ESP should be 0: " . ($newModESP == '0' ? '✅ CORRECT' : '❌ INCORRECT') . " (actual: {$newModESP})\n";
    
    // Check if next year tables exist
    $nextYearTable = $Account->getAccountsTableName($nextSY);
    $tableCheck = $Account->query("SHOW TABLES LIKE '{$nextYearTable}'");
    echo "- Table {$nextYearTable} exists: " . (!empty($tableCheck) ? '✅ YES' : '❌ NO') . "\n";
    
    // Check balance migration
    if (!empty($tableCheck)) {
        $oldBalanceCheck = $Account->query("
            SELECT COUNT(*) as count, SUM(old_balance) as total 
            FROM {$nextYearTable} 
            WHERE old_balance > 0
        ");
        
        $balanceData = $oldBalanceCheck[0][0];
        echo "- Accounts with old_balance: " . $balanceData['count'] . "\n";
        echo "- Total migrated balance: ₱" . number_format($balanceData['total'], 2) . "\n";
    }
    
    echo "\n";
}

/**
 * Test config updates separately
 */
function testConfigUpdates() {
    echo "=== Testing Master Config Updates ===\n\n";
    
    $Account = new Account();
    
    echo "Testing Opening Period Config (MOD_ESP = 1):\n";
    $openingUpdates = $Account->updateSchoolYearConfigs(2024, 2025, 'opening');
    foreach ($openingUpdates as $update) {
        echo "✓ {$update}\n";
    }
    showCurrentConfigs($Account);
    
    echo "Testing Closing Period Config (MOD_ESP = 0, ACTIVE_SY = 2025):\n";
    $closingUpdates = $Account->updateSchoolYearConfigs(2024, 2025, 'closing');
    foreach ($closingUpdates as $update) {
        echo "✓ {$update}\n";
    }
    showCurrentConfigs($Account);
    
    echo "=== Config Testing Complete ===\n";
}

// Execute the migration if this script is run directly
if (php_sapi_name() === 'cli' || !isset($_SERVER['HTTP_HOST'])) {
    // Command line execution
    completeMigrationWithConfigs();
    echo "\n";
    testConfigUpdates();
} else {
    // Web interface execution
    echo "<pre>";
    completeMigrationWithConfigs();
    echo "\n";
    testConfigUpdates();
    echo "</pre>";
}

?>
