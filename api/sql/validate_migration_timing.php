<?php
/**
 * Migration Timing Validation Script
 * 
 * This script checks if migration is allowed based on current system date
 * and billing cutoffs. It provides detailed information about the current
 * business period and migration permissions.
 * 
 * Features:
 * - Real-time date validation
 * - Business period identification
 * - Migration permission checking
 * - Override options for testing
 * 
 * <AUTHOR> Name
 * @date 2025-01-XX
 */

// Include CakePHP framework
require_once dirname(__FILE__) . '/../config/core.php';
require_once CAKE . 'bootstrap.php';

// Import required models
App::import('Model', 'Account');

/**
 * Main validation function
 */
function validateMigrationTiming() {
    echo "=== Migration Timing Validation ===\n\n";
    
    // Initialize Account model
    $Account = new Account();
    
    // Configuration
    $currentSY = 2024;
    $nextSY = 2025;
    
    echo "Configuration:\n";
    echo "- Current SY: {$currentSY}\n";
    echo "- Next SY: {$nextSY}\n";
    echo "- System Date: " . date('Y-m-d H:i:s') . "\n\n";
    
    // Step 1: Get billing cutoffs
    echo "Step 1: Retrieving billing cutoffs...\n";
    $cutoffs = $Account->getBillingCutoffs($currentSY);
    
    if (!$cutoffs) {
        echo "❌ ERROR: No billing cutoffs found for SY {$currentSY}\n";
        return;
    }
    
    displayBillingCutoffs($cutoffs, $currentSY);
    
    // Step 2: Validate migration timing
    echo "Step 2: Validating migration timing...\n";
    $validation = $Account->validateMigrationTiming($currentSY, false);
    
    displayValidationResults($validation);
    
    // Step 3: Test different scenarios
    echo "Step 3: Testing migration scenarios...\n";
    testMigrationScenarios($Account, $currentSY, $nextSY);
    
    echo "=== Validation Complete ===\n";
}

/**
 * Display billing cutoffs information
 */
function displayBillingCutoffs($cutoffs, $sy) {
    echo "Billing Cutoffs for SY {$sy}:\n";
    echo "┌─────────────────┬─────────────┐\n";
    echo "│ Period          │ Date        │\n";
    echo "├─────────────────┼─────────────┤\n";
    echo "│ Opening Date    │ " . str_pad($cutoffs['opening_date'], 11) . " │\n";
    echo "│ Start Bill Date │ " . str_pad($cutoffs['start_bill_date'], 11) . " │\n";
    echo "│ End Bill Date   │ " . str_pad($cutoffs['end_bill_date'], 11) . " │\n";
    echo "│ Closing Date    │ " . str_pad($cutoffs['closing_date'], 11) . " │\n";
    echo "└─────────────────┴─────────────┘\n\n";
}

/**
 * Display validation results
 */
function displayValidationResults($validation) {
    $statusIcons = array(
        'ALLOWED' => '✅',
        'CAUTION' => '⚠️ ',
        'WARNING' => '⚠️ ',
        'BLOCKED' => '❌',
        'ERROR' => '❌'
    );
    
    $icon = isset($statusIcons[$validation['status']]) ? $statusIcons[$validation['status']] : '❓';
    
    echo "Validation Results:\n";
    echo "┌─────────────────┬─────────────────────────────────────┐\n";
    echo "│ Current Date    │ " . str_pad($validation['current_date'], 35) . " │\n";
    echo "│ Current Period  │ " . str_pad($validation['current_period'], 35) . " │\n";
    echo "│ Status          │ " . str_pad($icon . ' ' . $validation['status'], 35) . " │\n";
    echo "│ Migration       │ " . str_pad($validation['allowed'] ? '✅ ALLOWED' : '❌ BLOCKED', 35) . " │\n";
    echo "└─────────────────┴─────────────────────────────────────┘\n";
    
    echo "\nMessage: " . $validation['message'] . "\n";
    
    if (!empty($validation['warnings'])) {
        echo "\n⚠️  WARNINGS:\n";
        foreach ($validation['warnings'] as $warning) {
            echo "   • {$warning}\n";
        }
    }
    
    if (!empty($validation['recommendations'])) {
        echo "\n📋 RECOMMENDATIONS:\n";
        foreach ($validation['recommendations'] as $rec) {
            echo "   • {$rec}\n";
        }
    }
    
    echo "\n";
}

/**
 * Test different migration scenarios
 */
function testMigrationScenarios($Account, $currentSY, $nextSY) {
    echo "Testing Migration Scenarios:\n\n";
    
    // Scenario 1: Normal migration (no override)
    echo "Scenario 1: Normal Migration (no override)\n";
    echo "-------------------------------------------\n";
    $result1 = $Account->quickMigrate($currentSY, $nextSY, null, false);
    displayMigrationResult($result1, "Normal Migration");
    
    // Scenario 2: Force override migration
    echo "\nScenario 2: Force Override Migration\n";
    echo "------------------------------------\n";
    $result2 = $Account->quickMigrate($currentSY, $nextSY, null, true);
    displayMigrationResult($result2, "Force Override");
    
    // Scenario 3: Custom date migration
    echo "\nScenario 3: Custom Date Migration\n";
    echo "---------------------------------\n";
    $customDate = '2025-04-07'; // End bill date
    $result3 = $Account->initializeNextSchoolYear($currentSY, $nextSY, $customDate, false);
    displayMigrationResult($result3, "Custom Date ({$customDate})");
}

/**
 * Display migration result summary
 */
function displayMigrationResult($result, $scenario) {
    $icon = $result['success'] ? '✅' : '❌';
    echo "{$icon} {$scenario}: " . ($result['success'] ? 'SUCCESS' : 'FAILED') . "\n";
    echo "   Message: " . $result['message'] . "\n";
    
    if (!empty($result['validation'])) {
        $val = $result['validation'];
        echo "   Period: " . $val['current_period'] . " (" . $val['status'] . ")\n";
        echo "   Allowed: " . ($val['allowed'] ? 'YES' : 'NO') . "\n";
    }
    
    if (!empty($result['warnings'])) {
        echo "   Warnings: " . count($result['warnings']) . " warning(s)\n";
    }
    
    if (!empty($result['errors'])) {
        echo "   Errors: " . count($result['errors']) . " error(s)\n";
        foreach ($result['errors'] as $error) {
            echo "     - {$error}\n";
        }
    }
    
    if ($result['success']) {
        echo "   Accounts Updated: " . $result['accounts_updated'] . "\n";
        echo "   Balances Migrated: " . $result['balances_migrated'] . "\n";
    }
}

/**
 * Show business period timeline
 */
function showBusinessTimeline($cutoffs) {
    echo "Business Period Timeline:\n";
    echo "========================\n\n";
    
    $currentDate = date('Y-m-d');
    $periods = array(
        array('name' => 'Pre-Opening', 'start' => '2024-01-01', 'end' => $cutoffs['opening_date']),
        array('name' => 'Early Enrollment', 'start' => $cutoffs['opening_date'], 'end' => $cutoffs['start_bill_date']),
        array('name' => 'Overlap Period', 'start' => $cutoffs['start_bill_date'], 'end' => $cutoffs['end_bill_date']),
        array('name' => 'Grace Period', 'start' => $cutoffs['end_bill_date'], 'end' => $cutoffs['closing_date']),
        array('name' => 'Post-Closing', 'start' => $cutoffs['closing_date'], 'end' => '2025-12-31')
    );
    
    foreach ($periods as $period) {
        $isCurrent = ($currentDate >= $period['start'] && $currentDate <= $period['end']);
        $marker = $isCurrent ? ' ← CURRENT' : '';
        echo sprintf("%-15s: %s to %s%s\n", $period['name'], $period['start'], $period['end'], $marker);
    }
    
    echo "\n";
}

// Execute the validation if this script is run directly
if (php_sapi_name() === 'cli' || !isset($_SERVER['HTTP_HOST'])) {
    // Command line execution
    validateMigrationTiming();
} else {
    // Web interface execution
    echo "<pre>";
    validateMigrationTiming();
    echo "</pre>";
}

?>
