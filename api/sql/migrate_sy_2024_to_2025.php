<?php
/**
 * School Year Migration Script: SY 2024 to SY 2025
 *
 * This script demonstrates how to use the Account model's initializeNextSchoolYear function
 * to migrate outstanding balances from SY 2024 to SY 2025.
 *
 * Usage:
 * 1. Run this script from the command line or web interface
 * 2. Review the results and verify the migration was successful
 * 3. Check the generated reports for any issues
 *
 * <AUTHOR> Name
 * @date 2025-01-XX
 */

// Bootstrap CakePHP framework properly
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// Define paths relative to this script
if (!defined('ROOT')) {
    define('ROOT', dirname(dirname(dirname(__FILE__))));
}
if (!defined('APP_DIR')) {
    define('APP_DIR', basename(dirname(__FILE__)));
}
if (!defined('CAKE_CORE_INCLUDE_PATH')) {
    define('CAKE_CORE_INCLUDE_PATH', '/Applications/XAMPP/xamppfiles/htdocs/cakephp');
}

// Additional required definitions
if (!defined('WEBROOT_DIR')) {
    define('WEBROOT_DIR', 'webroot');
}
if (!defined('WWW_ROOT')) {
    define('WWW_ROOT', dirname(__FILE__) . DS . 'webroot' . DS);
}
if (!defined('CORE_PATH')) {
    if (function_exists('ini_set') && ini_set('include_path', CAKE_CORE_INCLUDE_PATH . PATH_SEPARATOR . ROOT . DS . APP_DIR . DS . PATH_SEPARATOR . ini_get('include_path'))) {
        define('APP_PATH', null);
        define('CORE_PATH', null);
    } else {
        define('APP_PATH', ROOT . DS . APP_DIR . DS);
        define('CORE_PATH', CAKE_CORE_INCLUDE_PATH . DS);
    }
}

// Include CakePHP bootstrap
if (!include(CORE_PATH . 'cake' . DS . 'bootstrap.php')) {
    trigger_error("CakePHP core could not be found. Check the value of CAKE_CORE_INCLUDE_PATH.", E_USER_ERROR);
}

// Import required models
App::import('Model', 'Account');

/**
 * Main migration function
 */
function migrateSY2024to2025() {
    echo "=== School Year Migration: SY 2024 to SY 2025 ===\n\n";

    // Initialize Account model
    $Account = new Account();

    // Configuration
    $currentSY = 2024;
    $nextSY = 2025;
    $closingDate = '2025-04-07'; // Closing date for SY 2024

    echo "Configuration:\n";
    echo "- Current SY: {$currentSY}\n";
    echo "- Next SY: {$nextSY}\n";
    echo "- Closing Date: {$closingDate}\n\n";

    // Step 1: Get summary before migration
    echo "Step 1: Getting summary of outstanding balances before migration...\n";
    $summaryBefore = $Account->getOutstandingBalancesSummary($currentSY, $closingDate);

    echo "Summary for SY {$currentSY} (before migration):\n";
    echo "- Total Accounts: " . $summaryBefore['total_accounts'] . "\n";
    echo "- Accounts with Balance: " . $summaryBefore['accounts_with_balance'] . "\n";
    echo "- Fully Paid Accounts: " . $summaryBefore['fully_paid_accounts'] . "\n";
    echo "- Total Outstanding: ₱" . number_format($summaryBefore['total_outstanding'], 2) . "\n";
    echo "- Average Balance: ₱" . number_format($summaryBefore['average_balance'], 2) . "\n\n";

    // Step 2: Perform the migration
    echo "Step 2: Performing school year migration...\n";
    $result = $Account->initializeNextSchoolYear($currentSY, $nextSY, $closingDate);

    // Step 3: Display results
    echo "Migration Results:\n";
    echo "- Success: " . ($result['success'] ? 'YES' : 'NO') . "\n";
    echo "- Message: " . $result['message'] . "\n";
    echo "- Accounts Updated: " . $result['accounts_updated'] . "\n";
    echo "- Balances Migrated: " . $result['balances_migrated'] . "\n\n";

    if (!empty($result['steps_completed'])) {
        echo "Steps Completed:\n";
        foreach ($result['steps_completed'] as $step) {
            echo "✓ {$step}\n";
        }
        echo "\n";
    }

    if (!empty($result['errors'])) {
        echo "Errors Encountered:\n";
        foreach ($result['errors'] as $error) {
            echo "✗ {$error}\n";
        }
        echo "\n";
    }

    // Step 4: Verify migration
    if ($result['success']) {
        echo "Step 3: Verifying migration...\n";
        verifyMigration($Account, $currentSY, $nextSY, $closingDate);
    }

    echo "=== Migration Complete ===\n";
}

/**
 * Verify the migration was successful
 */
function verifyMigration($Account, $currentSY, $nextSY, $closingDate) {
    // Check if next year tables exist and have data
    $checkTables = array(
        "accounts_{$nextSY}",
        "account_fees_{$nextSY}",
        "account_schedules_{$nextSY}"
    );

    foreach ($checkTables as $table) {
        $result = $Account->query("SELECT COUNT(*) as count FROM {$table}");
        $count = $result[0][0]['count'];
        echo "- Table {$table}: {$count} records\n";
    }

    // Check old_balance migration
    $oldBalanceCheck = $Account->query("
        SELECT
            COUNT(*) as total_with_old_balance,
            SUM(old_balance) as total_old_balance
        FROM accounts_{$nextSY}
        WHERE old_balance > 0
    ");

    $oldBalanceData = $oldBalanceCheck[0][0];
    echo "- Accounts with old_balance: " . $oldBalanceData['total_with_old_balance'] . "\n";
    echo "- Total old_balance amount: ₱" . number_format($oldBalanceData['total_old_balance'], 2) . "\n";

    // Compare with original outstanding balances
    $sourceAccountsTable = ($currentSY == 2024) ? 'accounts' : "accounts_{$currentSY}";
    $originalOutstanding = $Account->query("
        SELECT SUM(outstanding_balance) as total_outstanding
        FROM {$sourceAccountsTable}
        WHERE outstanding_balance > 0
    ");

    $originalAmount = $originalOutstanding[0][0]['total_outstanding'];
    echo "- Original outstanding balance: ₱" . number_format($originalAmount, 2) . "\n";

    // Verify amounts match
    $difference = abs($originalAmount - $oldBalanceData['total_old_balance']);
    if ($difference < 0.01) {
        echo "✓ Balance migration verified - amounts match!\n";
    } else {
        echo "✗ Warning: Balance mismatch of ₱" . number_format($difference, 2) . "\n";
    }

    echo "\n";
}

/**
 * Generate detailed report of accounts with outstanding balances
 */
function generateDetailedReport($Account, $sy, $closingDate) {
    echo "=== Detailed Report: Accounts with Outstanding Balances ===\n\n";

    $sql = "
    SELECT
      b1.account_id,
      b1.due_amount as outstanding_balance,
      b1.id as latest_billing_id
    FROM
      billings b1
      INNER JOIN
        (SELECT
          account_id,
          MAX(id) AS latest_billing_id
        FROM
          billings
        WHERE due_date = '{$closingDate}'
          AND sy = {$sy}
        GROUP BY account_id) AS b2
        ON (b1.id = b2.latest_billing_id)
    WHERE b1.due_amount > 0
    ORDER BY b1.due_amount DESC
    LIMIT 20
    ";

    $results = $Account->query($sql);

    echo "Top 20 Accounts with Outstanding Balances:\n";
    echo str_pad("Account ID", 12) . str_pad("Outstanding", 15) . "Billing ID\n";
    echo str_repeat("-", 40) . "\n";

    foreach ($results as $row) {
        $data = $row['b1'];
        echo str_pad($data['account_id'], 12) .
             str_pad("₱" . number_format($data['outstanding_balance'], 2), 15) .
             $data['latest_billing_id'] . "\n";
    }

    echo "\n";
}

// Execute the migration if this script is run directly
if (php_sapi_name() === 'cli' || !isset($_SERVER['HTTP_HOST'])) {
    // Command line execution
    migrateSY2024to2025();
} else {
    // Web interface execution
    echo "<pre>";
    migrateSY2024to2025();
    echo "</pre>";
}

?>
