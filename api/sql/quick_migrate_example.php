<?php
/**
 * Quick Migration Examples
 * 
 * This file demonstrates how to use the quickMigrate function for easy school year transitions.
 * The quickMigrate function automatically handles table naming conventions and uses standard
 * closing dates.
 * 
 * <AUTHOR> Name
 * @date 2025-01-XX
 */

// Include CakePHP framework
require_once dirname(__FILE__) . '/../config/core.php';
require_once CAKE . 'bootstrap.php';

// Import required models
App::import('Model', 'Account');

echo "=== Quick Migration Examples ===\n\n";

// Initialize Account model
$Account = new Account();

// Example 1: SY 2024 to SY 2025 (uses default closing date 2025-04-07)
echo "Example 1: Quick Migration SY 2024 to SY 2025\n";
echo "--------------------------------------------\n";
$result1 = $Account->quickMigrate(2024, 2025);
echo "Result: " . ($result1['success'] ? 'SUCCESS' : 'FAILED') . "\n";
echo "Message: " . $result1['message'] . "\n\n";

// Example 2: SY 2025 to SY 2026 (uses default closing date 2026-04-07)
echo "Example 2: Quick Migration SY 2025 to SY 2026\n";
echo "--------------------------------------------\n";
$result2 = $Account->quickMigrate(2025, 2026);
echo "Result: " . ($result2['success'] ? 'SUCCESS' : 'FAILED') . "\n";
echo "Message: " . $result2['message'] . "\n\n";

// Example 3: Custom closing date
echo "Example 3: Quick Migration with Custom Closing Date\n";
echo "--------------------------------------------------\n";
$result3 = $Account->quickMigrate(2026, 2027, '2027-03-31');
echo "Result: " . ($result3['success'] ? 'SUCCESS' : 'FAILED') . "\n";
echo "Message: " . $result3['message'] . "\n\n";

// Example 4: Get summary before migration
echo "Example 4: Get Summary Before Migration\n";
echo "-------------------------------------\n";
$summary = $Account->getOutstandingBalancesSummary(2024, '2025-04-07');
echo "Total Accounts: " . $summary['total_accounts'] . "\n";
echo "Accounts with Balance: " . $summary['accounts_with_balance'] . "\n";
echo "Total Outstanding: ₱" . number_format($summary['total_outstanding'], 2) . "\n\n";

// Example 5: Table name helpers
echo "Example 5: Table Name Helpers\n";
echo "----------------------------\n";
echo "SY 2024 accounts table: " . $Account->getAccountsTableName(2024) . "\n";
echo "SY 2025 accounts table: " . $Account->getAccountsTableName(2025) . "\n";
echo "SY 2026 accounts table: " . $Account->getAccountsTableName(2026) . "\n";
echo "SY 2024 fees table: " . $Account->getAccountFeesTableName(2024) . "\n";
echo "SY 2025 fees table: " . $Account->getAccountFeesTableName(2025) . "\n";
echo "SY 2024 schedules table: " . $Account->getAccountSchedulesTableName(2024) . "\n";
echo "SY 2025 schedules table: " . $Account->getAccountSchedulesTableName(2025) . "\n\n";

echo "=== Examples Complete ===\n";

?>
