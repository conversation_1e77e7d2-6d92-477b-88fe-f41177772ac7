-- Initialize Account Tables for SY 2026
-- This script creates the necessary tables for the next school year (2026)
-- Generated automatically based on the pattern for SY 2025

CREATE TABLE IF NOT EXISTS `accounts_2026` (
  `id` char(8) NOT NULL,
  `account_type` char(10) DEFAULT NULL,
  `sy` int(4) DEFAULT NULL,
  `ref_no` char(15) NOT NULL,
  `account_details` text DEFAULT NULL,
  `payment_scheme` char(5) DEFAULT NULL COMMENT 'CASH,INSTL',
  `assessment_total` decimal(10,2) DEFAULT NULL,
  `subsidy_status` char(5) DEFAULT NULL,
  `discount_amount` decimal(10,2) DEFAULT NULL,
  `payment_total` decimal(10,2) NOT NULL,
  `old_balance` decimal(10,2) DEFAULT NULL,
  `outstanding_balance` decimal(10,2) DEFAULT NULL,
  `module_balance` decimal(10,2) DEFAULT NULL,
  `rounding_off` decimal(6,5) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

CREATE TABLE IF NOT EXISTS `account_fees_2026` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` char(8) DEFAULT NULL,
  `fee_id` char(5) DEFAULT NULL,
  `due_amount` decimal(10,2) DEFAULT NULL,
  `paid_amount` decimal(10,2) DEFAULT NULL,
  `adjust_amount` decimal(10,2) DEFAULT NULL,
  `percentage` decimal(5,2) DEFAULT NULL,
  `order` int(11) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

CREATE TABLE IF NOT EXISTS `account_schedules_2026` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_type_id` char(5) DEFAULT NULL,
  `account_id` char(8) DEFAULT NULL,
  `bill_month` varchar(10) DEFAULT NULL,
  `due_amount` decimal(10,2) DEFAULT NULL,
  `paid_amount` decimal(10,2) DEFAULT NULL,
  `due_date` date DEFAULT NULL,
  `paid_date` date DEFAULT NULL,
  `status` char(5) DEFAULT NULL,
  `order` int(11) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- Add indexes for better performance
CREATE INDEX idx_accounts_2026_sy ON accounts_2026(sy);
CREATE INDEX idx_accounts_2026_account_type ON accounts_2026(account_type);
CREATE INDEX idx_account_fees_2026_account_id ON account_fees_2026(account_id);
CREATE INDEX idx_account_schedules_2026_account_id ON account_schedules_2026(account_id);
CREATE INDEX idx_account_schedules_2026_due_date ON account_schedules_2026(due_date);
