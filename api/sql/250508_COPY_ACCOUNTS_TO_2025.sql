-- Script to copy records from accounts to accounts_2025
-- Set SY to 2025, reset amount fields to zero, and set account_details to INIT_ACCOUNT

-- First, truncate the accounts_2025 table to ensure we start fresh
TRUNCATE TABLE accounts_2025;
TRUNCATE TABLE account_fees_2025;
TRUNCATE TABLE account_schedules_2025;


-- Copy records from accounts to accounts_2025 with the specified modifications
INSERT INTO accounts_2025 (
    id,
    account_type,
    sy,
    ref_no,
    account_details,
    payment_scheme,
    assessment_total,
    subsidy_status,
    discount_amount,
    payment_total,
    old_balance,
    outstanding_balance,
    module_balance,
    rounding_off,
    created,
    modified
)
SELECT
    id,
    account_type,
    2025 AS sy,
    '' AS ref_no,
    'INIT_ACCOUNT' AS account_details,
    NULL AS payment_scheme,
    0.00 AS assessment_total,
    subsidy_status,
    0.00 AS discount_amount,
    0.00 AS payment_total,
    0.00 AS old_balance,
    0.00 AS outstanding_balance,
    0.00 AS module_balance,
    0.00 AS rounding_off,
    NOW() AS created,
    NOW() AS modified
FROM accounts;

-- Display the count of records copied
SELECT CONCAT('Copied ', COUNT(*), ' records from accounts to accounts_2025') AS Result FROM accounts_2025;
