<?php
/**
 * Smart School Year Migration Script: SY 2024 to SY 2025
 * 
 * This script uses the billing_cutoffs table to determine the correct migration timing
 * and follows the overlapping enrollment business process.
 * 
 * Key Features:
 * - Uses closing_date from billing_cutoffs as migration trigger
 * - Uses end_bill_date for outstanding balance calculation
 * - Respects overlapping enrollment periods
 * - Provides detailed business process information
 * 
 * <AUTHOR> Name
 * @date 2025-01-XX
 */

// Include CakePHP framework
require_once dirname(__FILE__) . '/../config/core.php';
require_once CAKE . 'bootstrap.php';

// Import required models
App::import('Model', 'Account');

/**
 * Smart migration function that uses billing_cutoffs data
 */
function smartMigrateSY2024to2025() {
    echo "=== Smart School Year Migration: SY 2024 to SY 2025 ===\n\n";
    
    // Initialize Account model
    $Account = new Account();
    
    // Configuration
    $currentSY = 2024;
    $nextSY = 2025;
    
    echo "Configuration:\n";
    echo "- Current SY: {$currentSY}\n";
    echo "- Next SY: {$nextSY}\n\n";
    
    // Step 1: Get billing cutoffs data
    echo "Step 1: Retrieving billing cutoffs data...\n";
    $cutoffs = $Account->getBillingCutoffs($currentSY);
    
    if (!$cutoffs) {
        echo "❌ ERROR: No billing cutoffs found for SY {$currentSY}\n";
        echo "Please ensure billing_cutoffs table has data for SY {$currentSY}\n";
        return;
    }
    
    echo "Billing Cutoffs for SY {$currentSY}:\n";
    echo "- Start Bill Date: " . $cutoffs['start_bill_date'] . "\n";
    echo "- End Bill Date: " . $cutoffs['end_bill_date'] . "\n";
    echo "- Opening Date: " . $cutoffs['opening_date'] . "\n";
    echo "- Closing Date: " . $cutoffs['closing_date'] . "\n\n";
    
    // Step 2: Check current date vs closing date
    $currentDate = date('Y-m-d');
    $closingDate = $cutoffs['closing_date'];
    $endBillDate = $cutoffs['end_bill_date'];
    
    echo "Step 2: Checking migration timing...\n";
    echo "- Current Date: {$currentDate}\n";
    echo "- Closing Date: {$closingDate}\n";
    echo "- End Bill Date: {$endBillDate}\n";
    
    if ($currentDate < $closingDate) {
        echo "⚠️  WARNING: Current date is before closing date!\n";
        echo "Migration should typically run on or after: {$closingDate}\n";
        echo "Do you want to proceed anyway? (This is for testing purposes)\n\n";
    } else {
        echo "✅ Ready for migration (current date >= closing date)\n\n";
    }
    
    // Step 3: Show business process context
    echo "Step 3: Business Process Context...\n";
    showBusinessProcessStatus($cutoffs, $currentDate);
    
    // Step 4: Get summary before migration
    echo "Step 4: Getting summary of outstanding balances...\n";
    $summaryBefore = $Account->getOutstandingBalancesSummary($currentSY, $endBillDate);
    
    echo "Summary for SY {$currentSY} (using end_bill_date: {$endBillDate}):\n";
    echo "- Total Accounts: " . $summaryBefore['total_accounts'] . "\n";
    echo "- Accounts with Balance: " . $summaryBefore['accounts_with_balance'] . "\n";
    echo "- Fully Paid Accounts: " . $summaryBefore['fully_paid_accounts'] . "\n";
    echo "- Total Outstanding: ₱" . number_format($summaryBefore['total_outstanding'], 2) . "\n";
    echo "- Average Balance: ₱" . number_format($summaryBefore['average_balance'], 2) . "\n\n";
    
    // Step 5: Perform the migration
    echo "Step 5: Performing smart migration...\n";
    echo "Using end_bill_date ({$endBillDate}) for outstanding balance calculation\n";
    echo "Migration triggered by closing_date ({$closingDate})\n\n";
    
    $result = $Account->initializeNextSchoolYear($currentSY, $nextSY, $endBillDate);
    
    // Step 6: Display results
    echo "Migration Results:\n";
    echo "- Success: " . ($result['success'] ? 'YES' : 'NO') . "\n";
    echo "- Message: " . $result['message'] . "\n";
    echo "- Accounts Updated: " . $result['accounts_updated'] . "\n";
    echo "- Balances Migrated: " . $result['balances_migrated'] . "\n\n";
    
    if (!empty($result['steps_completed'])) {
        echo "Steps Completed:\n";
        foreach ($result['steps_completed'] as $step) {
            echo "✓ {$step}\n";
        }
        echo "\n";
    }
    
    if (!empty($result['errors'])) {
        echo "Errors Encountered:\n";
        foreach ($result['errors'] as $error) {
            echo "✗ {$error}\n";
        }
        echo "\n";
    }
    
    // Step 7: Verify migration
    if ($result['success']) {
        echo "Step 6: Verifying migration...\n";
        verifySmartMigration($Account, $currentSY, $nextSY, $endBillDate);
    }
    
    echo "=== Smart Migration Complete ===\n";
}

/**
 * Show business process status based on current date and cutoffs
 */
function showBusinessProcessStatus($cutoffs, $currentDate) {
    $openingDate = $cutoffs['opening_date'];
    $startBillDate = $cutoffs['start_bill_date'];
    $endBillDate = $cutoffs['end_bill_date'];
    $closingDate = $cutoffs['closing_date'];
    
    echo "Business Process Status:\n";
    
    if ($currentDate < $openingDate) {
        echo "📅 Period: Pre-Opening (Before Early Enrollment)\n";
        echo "   Status: SY 2024 active, SY 2025 not yet available\n";
    } elseif ($currentDate >= $openingDate && $currentDate < $startBillDate) {
        echo "📅 Period: Early Enrollment Period\n";
        echo "   Status: SY 2024 active, SY 2025 reservations accepted\n";
        echo "   Activities: Students can reserve spots for SY 2025\n";
    } elseif ($currentDate >= $startBillDate && $currentDate <= $endBillDate) {
        echo "📅 Period: Overlap Period\n";
        echo "   Status: Both SY 2024 and SY 2025 fully active\n";
        echo "   Activities: Full enrollment for SY 2025, SY 2024 billing continues\n";
    } elseif ($currentDate > $endBillDate && $currentDate < $closingDate) {
        echo "📅 Period: Grace Period\n";
        echo "   Status: SY 2024 billing ended, SY 2025 active\n";
        echo "   Activities: Final payments for SY 2024, normal SY 2025 operations\n";
    } else {
        echo "📅 Period: Post-Closing (Migration Time)\n";
        echo "   Status: SY 2024 closed, SY 2025 sole active year\n";
        echo "   Activities: Outstanding balances should be migrated\n";
    }
    echo "\n";
}

/**
 * Verify the smart migration was successful
 */
function verifySmartMigration($Account, $currentSY, $nextSY, $endBillDate) {
    // Check if next year tables exist and have data
    $checkTables = array(
        "accounts_{$nextSY}",
        "account_fees_{$nextSY}",
        "account_schedules_{$nextSY}"
    );
    
    foreach ($checkTables as $table) {
        $result = $Account->query("SELECT COUNT(*) as count FROM {$table}");
        $count = $result[0][0]['count'];
        echo "- Table {$table}: {$count} records\n";
    }
    
    // Check old_balance migration
    $oldBalanceCheck = $Account->query("
        SELECT 
            COUNT(*) as total_with_old_balance,
            SUM(old_balance) as total_old_balance
        FROM accounts_{$nextSY} 
        WHERE old_balance > 0
    ");
    
    $oldBalanceData = $oldBalanceCheck[0][0];
    echo "- Accounts with old_balance: " . $oldBalanceData['total_with_old_balance'] . "\n";
    echo "- Total old_balance amount: ₱" . number_format($oldBalanceData['total_old_balance'], 2) . "\n";
    
    // Compare with original outstanding balances
    $sourceAccountsTable = $Account->getAccountsTableName($currentSY);
    $originalOutstanding = $Account->query("
        SELECT SUM(outstanding_balance) as total_outstanding 
        FROM {$sourceAccountsTable} 
        WHERE outstanding_balance > 0
    ");
    
    $originalAmount = $originalOutstanding[0][0]['total_outstanding'];
    echo "- Original outstanding balance ({$sourceAccountsTable}): ₱" . number_format($originalAmount, 2) . "\n";
    echo "- End bill date used: {$endBillDate}\n";
    
    // Verify amounts match
    $difference = abs($originalAmount - $oldBalanceData['total_old_balance']);
    if ($difference < 0.01) {
        echo "✅ Balance migration verified - amounts match!\n";
    } else {
        echo "⚠️  Warning: Balance mismatch of ₱" . number_format($difference, 2) . "\n";
    }
    
    echo "\n";
}

// Execute the migration if this script is run directly
if (php_sapi_name() === 'cli' || !isset($_SERVER['HTTP_HOST'])) {
    // Command line execution
    smartMigrateSY2024to2025();
} else {
    // Web interface execution
    echo "<pre>";
    smartMigrateSY2024to2025();
    echo "</pre>";
}

?>
