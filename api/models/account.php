<?php
class Account extends AppModel {
	var $name = 'Account';
	//var $useDbConfig = 'sfm';
	//The Associations below have been created with all possible keys, those that are not needed can be removed
	var $belongsTo = array(
		'Student' => array(
			'className' => 'Student',
			'foreignKey' => 'id',
			'dependent' => false,
			'conditions' => '',
			'fields' => '',
			'order' => '',
			'limit' => '',
			'offset' => '',
			'exclusive' => '',
			'finderQuery' => '',
			'counterQuery' => ''
		),
		'Inquiry' => array(
			'className' => 'Inquiry',
			'foreignKey' => 'id',
			'dependent' => false,
			'conditions' => '',
			'fields' => array(
				'Inquiry.id',
				'Inquiry.gender',
				'Inquiry.short_name',
				'Inquiry.full_name',
				'Inquiry.first_name',
				'Inquiry.last_name',
				'Inquiry.class_name',
				'Inquiry.year_level_id',
				'Inquiry.program_id'
			),
			'order' => '',
			'limit' => '',
			'offset' => '',
			'exclusive' => '',
			'finderQuery' => '',
			'counterQuery' => ''
		),
	);
	var $hasMany = array(
		'AccountAdjustment' => array(
			'className' => 'AccountAdjustment',
			'foreignKey' => 'account_id',
			'dependent' => false,
			'conditions' => '',
			'fields' => '',
			'order' => '',
			'limit' => '',
			'offset' => '',
			'exclusive' => '',
			'finderQuery' => '',
			'counterQuery' => ''
		),
		'AccountFee' => array(
			'className' => 'AccountFee',
			'foreignKey' => 'account_id',
			'dependent' => false,
			'conditions' => '',
			'fields' => '',
			'order' => '',
			'limit' => '',
			'offset' => '',
			'exclusive' => '',
			'finderQuery' => '',
			'counterQuery' => ''
		),
		'AccountHistory' => array(
			'className' => 'AccountHistory',
			'foreignKey' => 'account_id',
			'dependent' => false,
			'conditions' => '',
			'fields' => '',
			'order' => '',
			'limit' => '',
			'offset' => '',
			'exclusive' => '',
			'finderQuery' => '',
			'counterQuery' => ''
		),
		'AccountSchedule' => array(
			'className' => 'AccountSchedule',
			'foreignKey' => 'account_id',
			'dependent' => false,
			'conditions' => '',
			'fields' => '',
			'order' => array('AccountSchedule.order'=>'ASC','AccountSchedule.transaction_type_id'=>'DESC'),
			'limit' => '',
			'offset' => '',
			'exclusive' => '',
			'finderQuery' => '',
			'counterQuery' => ''
		),
		'Ledger' => array(
			'className' => 'Ledger',
			'foreignKey' => 'account_id',
			'dependent' => false,
			'conditions' => '',
			'fields' => '',
			'order' => '',
			'limit' => '',
			'offset' => '',
			'exclusive' => '',
			'finderQuery' => '',
			'counterQuery' => ''
		),
		'AccountTransaction' => array(
			'className' => 'AccountTransaction',
			'foreignKey' => 'account_id',
			'dependent' => false,
			'conditions' => '',
			'fields' => '',
			'order' => '',
			'limit' => '',
			'offset' => '',
			'exclusive' => '',
			'finderQuery' => '',
			'counterQuery' => ''
		)
	);
	function beforeFind($queryData){
		//pr($queryData); exit();
		if($conds=$queryData['conditions']){
			foreach($conds as $i=>$cond){
				if(!is_array($cond))
					break;
				$keys =  array_keys($cond);

				$search = ['Account.name LIKE','Account.first_name LIKE','Account.middle_name','Account.last_name','Account.refresh'];

				if(in_array($search[1],$keys)){
					$val = array_values($cond);
					$account_type = 'student';
					if(isset($_GET['account_type'])){
						$account_type = $_GET['account_type'];
					}
					if($account_type=='student')
						$students = $this->Student->findByName($val[1]);
					else if($account_type=='inquiry')
						$students = $this->Inquiry->findByName($val[1]);
					$student_ids= array_keys($students);
					unset($cond['Account.first_name LIKE']);
					unset($cond['Account.middle_name LIKE']);
					unset($cond['Account.last_name LIKE']);
					unset($cond['Account.name LIKE']);
					unset($cond['Account.id LIKE']);
					$cond['Account.id']=$student_ids;
				}
				
				if(in_array('Account.sy',$keys)):
					$sy =  $cond['Account.sy'];
					unset($cond['Account.sy']);
					$this->udpateEspSource($sy);
				endif;
				
				if(in_array($search[4],$keys)):
					unset($cond['Account.refresh']);
				endif;
				$conds[$i]=$cond;
			}
			//pr($conds);exit();
			$queryData['conditions']=$conds;
		}

		return $queryData;
	}

	function postTransaction($account_id, $trnx){
		$A = $this->findById($account_id);
		// Update account balances and totals
		$ACC = $A['Account'];
		if($trnx['flag']=='-'):
			$amount =  $trnx['amount'];
			$ACC['outstanding_balance'] -= $amount;
			$ACC['payment_total'] += $amount;

			// Account
			$ACD = array();
			$ACD['id'] =  $ACC['id'];
			$ACD['outstanding_balance'] =  $ACC['outstanding_balance'];
			$ACD['payment_total'] =  $ACC['payment_total'];
			$this->save($ACD);

			// Account History
			$ACH = array();
			$ACH['account_id'] = $ACC['id'];
			$ACH['total_due'] =  $ACC['assessment_total'];
			$ACH['total_paid'] =  $ACC['payment_total'];
			$ACH['balance'] =  $ACC['outstanding_balance'];
			$ACH['transac_date'] =  $trnx['transac_date'];
			$ACH['transac_time'] =  date('h:i:s',time());
			$ACH['amount'] =  $amount;
			$ACH['ref_no'] =  $trnx['ref_no'];
			$ACH['details'] =  $trnx['details'];
			$ACH['flag'] =  $trnx['flag'];
			$this->AccountHistory->save($ACH);

			// Account Fees
			$ACF = array();
			foreach($A['AccountFee'] as $AF):
				if($AF['fee_id']=='TUI'):
					$AF['paid_amount'] = $ACC['payment_total'];
					$this->AccountFee->save($AF);
				endif;
			endforeach;

			// Account Transaction
			$ACT = array();
			$ACT['account_id']= $ACC['id'];
			$ACT['transaction_type_id']= $trnx['transaction_type_id'];
			$ACT['ref_no']= $trnx['ref_no'];
			$ACT['amount']= $trnx['amount'];
			$this->AccountTransaction->save($ACT);
		endif;
	}

	function udpateEspSource($sy){
		$this->setSource('accounts_'.$sy);
		$this->AccountSchedule->setSource('account_schedules_'.$sy);
		$this->AccountFee->setSource('account_fees_'.$sy);
	}

	function generateId($prefix){
		$lastAccount = $this->find('first', array(
			'recursive'=>-1,
		    'conditions' => array('Account.id LIKE'=>$prefix.'%'),
		    'fields' => array('id'),
		    'order' => array('id' => 'desc')
		));

		// Extract the ID and remove the "LSO" prefix
		$lastIdStr = $lastAccount['Account']['id'];
		$numericPart = substr($lastIdStr, 3); // Remove the "LSO" prefix

		// Convert the numeric part to an integer and increment it
		$numericPartInt = (int)$numericPart;
		$newIdInt = $numericPartInt + 1;

		// Pad the new ID and prepend "LSO"
		$newIdStr = str_pad($newIdInt, 5, '0', STR_PAD_LEFT);
		$newAccountId = $prefix . $newIdStr;
		return $newAccountId;
	}
	protected function lookupAmount($obj,$key="amount"){
		$amount = $obj[$key];
		if(!is_numeric($amount)){
			$amount = floatval(str_replace(",", "", $amount));
		}
		return $amount;
	}
	protected function getEndBal($arr,$key){
		$amount=0;
		$hasItem = count($arr)>0;
		if($hasItem):
			$lastItem = $this->getEndItem($arr);
			if($key=='paysched'):
				$amount = $this->lookupAmount($lastItem,'total_bal');
			elseif($key=='ledger'):
				$amount = $this->lookupAmount($lastItem,'bal');
			endif;
		endif;
		return $amount;
	}
	protected function getEndItem(&$arr){
		return $arr[count($arr)-1];
	}
	function review($statement,$type="current"){
		$isValid = true;
		$PS = $statement['paysched_'.$type];
		$PSLen = count($PS);
		$PSEndBal =0;
		if($PSLen>0):
			$PSLast = $PS[count($PS)-1];
			$PSEndBal = $this->getEndBal($PS,'paysched');
		endif;
		$LE = $statement['ledger_'.$type];
		$LELen = count($LE);
		$LEEndBal =0;
		if($LELen>0):
			$LEEndBal =$this->getEndBal($LE,'ledger');
		endif;

		$isValid = $LEEndBal==$PSEndBal;

		if($PSLen>0 && $PSEndBal>0):
			$INIPY_0 = $PS[0]['status']!='PAID';
			$SBQPY_1 =0;
			if(isset($PS[1]['paid_amount'])):
				$SBQPY_1 = floatval(str_replace(",", "", $PS[1]['paid_amount']))>0;
			endif;
			$isValid = $isValid && !($INIPY_0 && $SBQPY_1);
		endif;

		return $isValid;

	}

	function ammend(&$statement,$type="current"){
		$ammended = array('corrected'=>false);
		$TUI = 0;
		$MOD = 0;
		$DSC = 0;
		$LOY = 0;
		$OLD = 0;
		$ACEC = 0;
		$VOU = 0;
		$OR_PAY = 0;
		$LE = $statement['ledger_'.$type];
		$TUIIndex =null;
		$voucher = array(
				'codes'=>array('AMFAV','AMSPO','AMOTF','EDV','AMTMC'),
				'details'=>array('FAV','SPV','AMEMD','TBD'),
			);
		foreach($LE as $index=>$entry):
			$details = $entry['details'];
			$code = $entry['transaction_type_id'];

			switch($code){
				case 'TUIXN':
					$TUI = $this->lookupAmount($entry);
					$TUIIndex = $index;
				break;
				case 'MODUL':
					$mod_amt = $this->lookupAmount($entry);
					if($entry['type']=='-')
						$mod_amt *=-1;
					$MOD += $mod_amt;
				break;
				case 'LYLTY':
					$LOY = $this->lookupAmount($entry);
				break;
				case 'OLDAC': case 'AMPEC':
					$old_amt = $this->lookupAmount($entry);
					if($entry['type']=='-')
						$old_amt *=-1;
					$OLD += $old_amt;

				break;
				case 'INIPY': case 'SBQPY': case 'FULLP':
					$payment = $this->lookupAmount($entry);
					if($entry['type']=='+')
						$payment *=-1;
					$OR_PAY += $payment;
				break;
				// Late & Regular ESC Voucher
				case  'AMLES': case 'AMRES':
					$VOU += $this->lookupAmount($entry);
				break;
				case 'ACECF':
					$ACEC += $this->lookupAmount($entry);
				break;
				default:
					if($details=='Subsidy' || $code=='DSESC'):
						$DSC = $this->lookupAmount($entry);
					endif;
					$isVCode = in_array($code, $voucher['codes']);
					$isVDetail = in_array($details, $voucher['details']);
					if($isVCode || $isVDetail) :
						$VOU += $this->lookupAmount($entry);
					endif;

				break;
			}
		endforeach;

		$PS = $statement['paysched_'.$type];
		$PSEndBal = $this->getEndBal($PS,'paysched');
		$PSTotals= $this->getEndItem($PS);
		$PSTotalDue = $this->lookupAmount($PSTotals,'total_due');

		$LE = &$statement['ledger_'.$type];
		$LEFees = $TUI + $MOD ;
		$LEPays = $DSC + $LOY  ;
		$LETotalDue = $LEFees - $LEPays;



		$Variance = $PSTotalDue -  $LETotalDue;

		if($Variance!=0 ):
			App::import('Model','PaymentPlan');
			$PP = new PaymentPlan();
			$isPSExccess = $PSTotalDue >$LETotalDue;
			$hasLE = count($LE)>0;
			$UPON_ENROL = $this->lookupAmount($PS[0],'due_amount');
			$TOT_SBQPY =  $LETotalDue -$UPON_ENROL;
			if($isPSExccess):
				// Make correction for Loyal Discount or Modules
				$isLOYDiff = $LOY>0  &&  $Variance==$LOY;
				$isMODiff = $MOD==0  && $Variance ==4950 ;


				// Loyalty Discounts Correction
				if($isLOYDiff):
					$TUIObj = &$LE[$TUIIndex];
					if($TUIObj['amount']!=39925){
						$TUIObj['amount']+=$LOY;
						$this->Ledger->updateEntry($TUIObj);
						$PP->recomputeLedger($LE);
						$ammended['corrected']=true;
					}
				endif;

				// Modules & eBook correction
				if($isMODiff):
					$TOT_SBQPY =  $PSTotalDue -$UPON_ENROL - $Variance;
				endif;

				if(!$hasLE):
					$_AID =  $statement['account']['id'];
					$_ESP =  floor($statement['account']['esp']);
					$EPP_REFNO =  $this->Ledger->generateREFNO($_ESP,'EPP');
					$_EPP_AMOUNT  = $PSTotalDue;

					$_EPP_date = date('Y-m-d', strtotime($PS[0]['due_date']));
					$_EPP_time = '01:23:45';

					$EPPObj = array(
						'ref_no'=>$EPP_REFNO,
						'account_id'=>$_AID,
						'type'=>'+',
						'transaction_type_id'=>'EXTPY',
						'details'=>'Ext. Payment Plan',
						'amount'=>$_EPP_AMOUNT,
						'esp'=>$_ESP,
						'transac_date'=>$_EPP_date,
						'transac_time'=>$_EPP_time,
						'notes'=>'LE00_ERR Correction',
					);
					$LE = array($EPPObj);
					$this->Ledger->insertEntry($EPPObj);
					$PP->recomputeLedger($LE);
					$statement['ledger_'.$type] = $LE;
					$ammended['corrected']=true;
				endif;
			endif;
			$PSAdj = array();
			$PSAdj['amount'] = $TOT_SBQPY;
			$PSAdj['apply_to'] = 'SBQPY';
			$PP->recomputePaysched($PS,$PSAdj);
			$PS=array_values($PS);
			$statement['paysched_'.$type] = $PS;
			$this->AccountSchedule->updateSchedule($PS);
			$ammended['corrected']=true;
		endif;
		if($OLD):
			App::import('Model','PaymentPlan');
			$PP = new PaymentPlan();
			$PSAdj = array();
			$PSAdj['amount'] =  $OLD;
			$PSAdj['apply_to'] = 'OLDAC';
			$PP->recomputePaysched($PS,$PSAdj);
			$PS = array_values($PS);
			$statement['paysched_'.$type] = $PS;
			$ammended['corrected']=true;

		endif;


		if($VOU):

			App::import('Model','PaymentPlan');
			$PP = new PaymentPlan();
			$PSAdj = array();
			$PSAdj['amount'] =  $VOU +$OR_PAY;
			$PSAdj['apply_to'] = 'AMFAV';
			//pr($PSAdj);
			$PP->recomputePaysched($PS,$PSAdj);
			$PS = array_values($PS);
			$statement['paysched_'.$type] = $PS;
			$this->AccountSchedule->updateSchedule($PS);
			$ammended['corrected']=true;
		endif;

		if($OR_PAY && $Variance==0 && !$VOU):
			App::import('Model','PaymentPlan');
				$PP = new PaymentPlan();
			$PSEndBal = $this->getEndBal($PS,'paysched');
			$PSTotals= &$this->getEndItem($PS);
			$PSTotalDue = $this->lookupAmount($PSTotals,'total_due');

			$LEEEndBal = $this->getEndBal($LE,'ledger');
			$LETotals= &$this->getEndItem($LE);

			$LETotalDue = $this->lookupAmount($LETotals,'bal');

			if($LETotalDue!=$PSTotalDue):
				$PSAdj = array();
				$PSAdj['amount'] =  $VOU +$OR_PAY;
				$PSAdj['apply_to'] = 'AMFAV';
				$PP->recomputePaysched($PS,$PSAdj);
				$PSAdj['apply_to'] = 'AMFAV';
				$PP->recomputePaysched($PS,$PSAdj);
				$PS = array_values($PS);
				$statement['paysched_'.$type] = $PS;
				$ammended['corrected']=true;
			endif;
		endif;

		return $ammended;
	}

	/**
	 * Forward a payment for a given account and ESP (Effective School Year Period).
	 *
	 * @param int $account_id The ID of the account.
	 * @param string $esp The Effective School Year Period.
	 * @param string $ref_no The reference number for the payment.
	 * @param float $amount The payment amount.
	 */
	function forwardPayment($account_id, $esp, $ref_no, $amount,$trnxObj,$source) {
	    // Find the account by account_id
	    $ACObj = $this->findById($account_id);

	    // Extract account information
	    $account = $ACObj['Account'];

	    // Update the account  with the payment amount
	    $this->updateAccount($account, $amount);

	    $accountInfo = array();
	    // Save balances in payment_plan and account if the account is valid
	    $skipAccountValidtion = true;
	    if ($account['is_valid'] ||$skipAccountValidtion ) {
	        $this->save($account);

	        // Distribute payment and update the payment schedule
	        $sched = $ACObj['AccountSchedule'];
	        $this->distributePayments($sched, $amount,$trnxObj['paid_date']);
			$this->logPaymentHistory($account,$ref_no,$amount,$trnxObj,$source);
	        $this->AccountSchedule->saveAll($sched);
	        $accountInfo['amount_paid'] = $amount;
	        $accountInfo['total_payments'] = $account['payment_total'];
	        $accountInfo['total_balance'] = $account['outstanding_balance'];
	    }

	    return $accountInfo;
	}

	/**
	 * Update the account and payment plan based on the payment amount.
	 *
	 * @param array $account Reference to the account array.
	 * @param array $plan Reference to the payment plan array.
	 * @param float $amount The payment amount.
	 */
	protected function updateAccount(&$account, $amount) {
	    $assess_total = $account['assessment_total'];
	    $discounts = $account['discount_amount'];
	    $payments = $account['payment_total'];
	    $outstanding_bal = $account['outstanding_balance'];
		$net_amount = $assess_total + $discounts -  $payments;
	    $account['is_valid'] = $outstanding_bal == $net_amount ;

	    // If the account is valid, update the total payments and balance
	    if ($account['is_valid']) {
	        $account['payment_total'] += $amount;
	        $account['outstanding_balance'] = $net_amount-$amount;
	    }
	}

	protected function logPaymentHistory($account,$ref_no,$amount,$trnxObj,$source){
		$tDate =  explode('~',date('Y-m-d~h:i:s',time()));
		$history = array(
			'account_id'=>$account['id'],
			'total_due'=>$account['assessment_total'],
			'total_paid'=>$account['payment_total'],
			'balance'=>$account['outstanding_balance'],
			'ref_no'=>$ref_no,
			'flag'=>'-',
			'details'=>$trnxObj['name'],
			'amount'=>$amount,
			'transac_date'=>$tDate[0],
			'transac_time'=>$tDate[1],
		);
		$this->AccountHistory->save($history);

		$transaction = array(
			'account_id'=>$account['id'],
			'transaction_type_id'=>$trnxObj['id'],
			'ref_no'=>$ref_no,
			'amount'=>$amount,
			'source'=>$source
		);

		$this->AccountTransaction->save($transaction);
	}

	/**
	 * Distribute the payment among the payment schedule based on the payment amount.
	 *
	 * @param array $schedule Reference to the payment schedule array.
	 * @param float $totalPayment The total payment amount to be distributed.
	 */
	protected function distributePayments(&$schedule, $totalPayment, $paidDate) {
	    foreach ($schedule as &$payment) {
	        // Check if the payment is unpaid and there's a remaining payment to be made
	        $isUNPAID = $payment['status'] === 'UNPAID' || $payment['status'] === 'NONE' || $payment['paid_amount']<$payment['due_amount'];
	        if ($isUNPAID && $totalPayment > 0) {
	            // Calculate the amount to be paid for this schedule
	            $amountToPay = min($totalPayment, $payment['due_amount'] - $payment['paid_amount']);

	            // Update paid amount and status
	            $payment['paid_amount'] += $amountToPay;
	            $totalPayment -= $amountToPay;

	            if ($payment['paid_amount'] == $payment['due_amount']) {
	                $payment['status'] = 'PAID';
					$payment['paid_date'] = $paidDate;
	            }

	            // Distribute excess payment to the next schedules
	            if ($totalPayment < 0) {
	                // Get the next schedule
	                $nextPayment = next($schedule);
	                if ($nextPayment !== false) {
	                    $nextPayment['paid_amount'] += abs($totalPayment);
	                    if ($nextPayment['paid_amount'] == $nextPayment['due_amount']) {
	                        $nextPayment['status'] = 'PAID';
	                        $nextPayment['paid_date'] = $paidDate;
	                    }
	                }
	            }
	        }
	    }

	    return $schedule;
	}

	function setupDetails($assessment,$new_account_id=null){
		$AObj = $assessment;
		$account = $AObj['Assessment'];
		$paysched = $AObj['AssessmentPaysched'];
		$fees = $AObj['AssessmentFee'];
		$esp = $account['esp'];
		// Setup Account Info
		if($new_account_id):
			$account_id = $new_account_id;
			$account['ref_no']=$AObj['Assessment']['id'];
			$account['account_details']='ENROLL-'.$esp;
			$account['account_type']='student';
		endif;
		$account['id'] = $account_id;

		// Setup Paysched
		foreach($paysched as $si=>$sched):
			unset($sched['id']);
			unset($sched['created']);
			unset($sched['modified']);
			$sched['account_id']=$account_id;
			$paysched[$si] = $sched;
		endforeach;

		// Setup Fees
		foreach($fees as $fi=>$fee):
			unset($sched['id']);
			unset($sched['created']);
			unset($sched['modified']);
			$fee['account_id']=$account_id;
			$fees[$fi] = $fee;
		endforeach;


		// Update Assessment status to enrolled
		$asmt = array('id'=>$AObj['Assessment']['id'],'status'=>'NROLD');
		$this->Student->Assessment->save($asmt);

		$AObj = array(
			'Account'=>$account,
			'AccountSchedule'=>$paysched,
			'AccountFee'=>$fees
		);
		$this->create();
		// Save New Account Details
		$this->saveAll($AObj);
		return $account_id;
	}

	function setupNewAccount(&$account,$new_account_id){
		$esp = $account['esp'];
		$ref_no = $account['ref_no'];
		// Setup Account Info
		if($new_account_id):
			$account['ref_no']=$ref_no;
			$account['account_details']='ENROLL-'.$esp;
			$account['account_type']='student';
			$account['id'] = $new_account_id;
		endif;
		$AID = $account['id'];
		$total_due = 0;

		// Fees
		$ledgers = array();
		$fee_order =1;
		foreach($account['fees'] as &$fee):
			$amount = $fee['amount'];
			$total_due+=$amount;
			$fee['account_id'] = $AID;
			$fee['due_amount'] = $amount;
			$fee['percentage'] = 0;
			$fee['adjust_amoutn'] = 0;
			$fee['paid_amount'] = 0;
			$fee['order'] = $fee_order;
			$fee_order++;
			$ledgerItem = $fee;
			$ledgerItem['ref_no'] =$ref_no;
			$ledgerItem['type'] ='+';
			$ledgerItem['transaction_type_id'] =$fee['fee_id'];
			$ledgerItem['details'] =$fee['name'];
			$ledgerItem['esp'] =$esp;

			$ledgerItem['transac_date'] =$account['date_enrolled'];
			$ledgerItem['transac_time'] ='01:23:45';

			$ledgers[]=$ledgerItem;
		endforeach;
		$account['ledgers']=$ledgers;
		// Payment Schedule
		$sched_order =1;
		foreach($account['paysched'] as &$sched):
			if(isset($sched['label'])):
				$label = $sched['label'];
				$ttype ='SBQPY';
				if($label=='Upon Registration'):
					$ttype ='REGFE';
				elseif($label=='Upon Enrollment'):
					$ttype ='INIPY';
				endif;
				$sched['transaction_type_id'] = $ttype;
			endif;
			$sched['account_id'] = $AID;
			$sched['paid_amount'] = 0;
			$sched['status'] ='NONE';
			$sched['order'] = $sched_order;

			$sched_order++;
		endforeach;
		$account['assessment_total'] = $total_due;
		$account['payment_total'] = 0;
		$account['outstanding_balance'] = $total_due;


		$this->save($account);
		$this->AccountFee->deleteAll(array('AccountFee.account_id'=>$AID));
		$this->AccountFee->saveAll($account['fees']);

		$this->AccountSchedule->deleteAll(array('AccountSchedule.account_id'=>$AID));
		$this->AccountSchedule->saveAll($account['paysched']);

		$this->Ledger->deleteAll(array('Ledger.account_id'=>$AID,'esp'=>$esp));
		$this->Ledger->saveAll($account['ledgers']);

	}

	/**
	 * Check if migration is allowed based on current date and billing cutoffs
	 *
	 * @param int $currentSY Current school year
	 * @param bool $forceOverride Allow override of date restrictions
	 * @return array Validation result with permission status and messages
	 */
	function validateMigrationTiming($currentSY, $forceOverride = false) {
		$validation = array(
			'allowed' => false,
			'status' => '',
			'message' => '',
			'current_date' => date('Y-m-d'),
			'current_period' => '',
			'recommendations' => array(),
			'warnings' => array()
		);

		// Get billing cutoffs
		$cutoffs = $this->getBillingCutoffs($currentSY);
		if (!$cutoffs) {
			$validation['status'] = 'ERROR';
			$validation['message'] = "No billing cutoffs found for SY {$currentSY}";
			return $validation;
		}

		$currentDate = $validation['current_date'];
		$openingDate = $cutoffs['opening_date'];
		$startBillDate = $cutoffs['start_bill_date'];
		$endBillDate = $cutoffs['end_bill_date'];
		$closingDate = $cutoffs['closing_date'];

		// Determine current period
		if ($currentDate < $openingDate) {
			$validation['current_period'] = 'Pre-Opening';
			$validation['status'] = 'BLOCKED';
			$validation['message'] = "Migration not allowed during Pre-Opening period";
			$validation['recommendations'][] = "Wait until opening date: {$openingDate}";
		} elseif ($currentDate >= $openingDate && $currentDate < $startBillDate) {
			$validation['current_period'] = 'Early Enrollment';
			$validation['status'] = 'WARNING';
			$validation['message'] = "Migration during Early Enrollment period - proceed with caution";
			$validation['warnings'][] = "SY {$currentSY} is still active with ongoing billing";
			$validation['warnings'][] = "Early enrollments for next SY may be affected";
			$validation['allowed'] = $forceOverride;
		} elseif ($currentDate >= $startBillDate && $currentDate <= $endBillDate) {
			$validation['current_period'] = 'Overlap Period';
			$validation['status'] = 'WARNING';
			$validation['message'] = "Migration during Overlap period - not recommended";
			$validation['warnings'][] = "Both school years are fully active";
			$validation['warnings'][] = "Ongoing billing for SY {$currentSY} will be affected";
			$validation['warnings'][] = "Students may still be making payments for current SY";
			$validation['allowed'] = $forceOverride;
		} elseif ($currentDate > $endBillDate && $currentDate < $closingDate) {
			$validation['current_period'] = 'Grace Period';
			$validation['status'] = 'CAUTION';
			$validation['message'] = "Migration during Grace period - acceptable but early";
			$validation['warnings'][] = "Some students may still make final payments";
			$validation['recommendations'][] = "Consider waiting until closing date: {$closingDate}";
			$validation['allowed'] = true;
		} else {
			$validation['current_period'] = 'Post-Closing';
			$validation['status'] = 'ALLOWED';
			$validation['message'] = "Migration allowed - optimal timing";
			$validation['allowed'] = true;
		}

		$validation['cutoffs'] = $cutoffs;
		return $validation;
	}

	/**
	 * Initialize accounts for the next school year and migrate outstanding balances
	 * This function handles the complete process of closing current SY and setting up next SY
	 *
	 * @param int $currentSY Current school year (e.g., 2024)
	 * @param int $nextSY Next school year (e.g., 2025)
	 * @param string $closingDate Closing date for current SY (e.g., '2025-04-07')
	 * @param bool $forceOverride Allow migration even if timing validation fails
	 * @return array Result summary with counts and status
	 */
	function initializeNextSchoolYear($currentSY, $nextSY, $closingDate, $forceOverride = false) {
		$result = array(
			'success' => false,
			'message' => '',
			'current_sy' => $currentSY,
			'next_sy' => $nextSY,
			'closing_date' => $closingDate,
			'steps_completed' => array(),
			'accounts_updated' => 0,
			'balances_migrated' => 0,
			'errors' => array(),
			'validation' => array(),
			'warnings' => array()
		);

		try {
			// Step 0: Validate migration timing
			$validation = $this->validateMigrationTiming($currentSY, $forceOverride);
			$result['validation'] = $validation;

			if (!$validation['allowed']) {
				$result['message'] = "Migration blocked: " . $validation['message'];
				$result['errors'][] = "Current period: " . $validation['current_period'];
				$result['errors'][] = "Current date: " . $validation['current_date'];
				if (!empty($validation['recommendations'])) {
					foreach ($validation['recommendations'] as $rec) {
						$result['errors'][] = "Recommendation: " . $rec;
					}
				}
				return $result;
			}

			// Add warnings to result if any
			if (!empty($validation['warnings'])) {
				$result['warnings'] = $validation['warnings'];
			}

			// Step 1: Create next year tables if they don't exist
			$this->createNextYearTables($nextSY);
			$result['steps_completed'][] = "Created tables for SY {$nextSY}";

			// Step 2: Update current year outstanding balances from latest billings
			$accountsUpdated = $this->updateOutstandingBalancesFromBillings($currentSY, $closingDate);
			$result['accounts_updated'] = $accountsUpdated;
			$result['steps_completed'][] = "Updated {$accountsUpdated} accounts with latest billing balances";

			// Step 3: Copy accounts to next year table
			$this->copyAccountsToNextYear($currentSY, $nextSY);
			$result['steps_completed'][] = "Copied accounts to accounts_{$nextSY} table";

			// Step 4: Migrate outstanding balances to old_balance in next year
			$balancesMigrated = $this->migrateOutstandingBalances($currentSY, $nextSY);
			$result['balances_migrated'] = $balancesMigrated;
			$result['steps_completed'][] = "Migrated {$balancesMigrated} outstanding balances to SY {$nextSY}";

			// Step 5: Update master_configs for school year transition
			$configUpdates = $this->updateSchoolYearConfigs($currentSY, $nextSY, 'closing');
			foreach ($configUpdates as $update) {
				$result['steps_completed'][] = $update;
			}

			$result['success'] = true;
			$result['message'] = "Successfully initialized SY {$nextSY} and migrated balances from SY {$currentSY}";

		} catch (Exception $e) {
			$result['errors'][] = $e->getMessage();
			$result['message'] = "Error during school year initialization: " . $e->getMessage();
		}

		return $result;
	}

	/**
	 * Create tables for the next school year
	 *
	 * @param int $nextSY Next school year
	 */
	protected function createNextYearTables($nextSY) {
		$sql = "
		CREATE TABLE IF NOT EXISTS `accounts_{$nextSY}` (
		  `id` char(8) NOT NULL,
		  `account_type` char(10) DEFAULT NULL,
		  `sy` int(4) DEFAULT NULL,
		  `ref_no` char(15) NOT NULL,
		  `account_details` text DEFAULT NULL,
		  `payment_scheme` char(5) DEFAULT NULL COMMENT 'CASH,INSTL',
		  `assessment_total` decimal(10,2) DEFAULT NULL,
		  `subsidy_status` char(5) DEFAULT NULL,
		  `discount_amount` decimal(10,2) DEFAULT NULL,
		  `payment_total` decimal(10,2) NOT NULL,
		  `old_balance` decimal(10,2) DEFAULT NULL,
		  `outstanding_balance` decimal(10,2) DEFAULT NULL,
		  `module_balance` decimal(10,2) DEFAULT NULL,
		  `rounding_off` decimal(6,5) DEFAULT NULL,
		  `created` datetime DEFAULT NULL,
		  `modified` datetime DEFAULT NULL,
		  PRIMARY KEY (`id`)
		) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

		CREATE TABLE IF NOT EXISTS `account_fees_{$nextSY}` (
		  `id` int(11) NOT NULL AUTO_INCREMENT,
		  `account_id` char(8) DEFAULT NULL,
		  `fee_id` char(5) DEFAULT NULL,
		  `due_amount` decimal(10,2) DEFAULT NULL,
		  `paid_amount` decimal(10,2) DEFAULT NULL,
		  `adjust_amount` decimal(10,2) DEFAULT NULL,
		  `percentage` decimal(5,2) DEFAULT NULL,
		  `order` int(11) DEFAULT NULL,
		  `created` datetime DEFAULT NULL,
		  `modified` datetime DEFAULT NULL,
		  PRIMARY KEY (`id`)
		) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

		CREATE TABLE IF NOT EXISTS `account_schedules_{$nextSY}` (
		  `id` int(11) NOT NULL AUTO_INCREMENT,
		  `transaction_type_id` char(5) DEFAULT NULL,
		  `account_id` char(8) DEFAULT NULL,
		  `bill_month` varchar(10) DEFAULT NULL,
		  `due_amount` decimal(10,2) DEFAULT NULL,
		  `paid_amount` decimal(10,2) DEFAULT NULL,
		  `due_date` date DEFAULT NULL,
		  `paid_date` date DEFAULT NULL,
		  `status` char(5) DEFAULT NULL,
		  `order` int(11) DEFAULT NULL,
		  `created` datetime DEFAULT NULL,
		  `modified` datetime DEFAULT NULL,
		  PRIMARY KEY (`id`)
		) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;
		";

		$this->query($sql);
	}

	/**
	 * Update outstanding balances from latest billings for the closing date
	 *
	 * @param int $currentSY Current school year
	 * @param string $closingDate Closing date (e.g., '2025-04-07')
	 * @return int Number of accounts updated
	 */
	protected function updateOutstandingBalancesFromBillings($currentSY, $closingDate) {
		// Use helper function to get correct table name
		$accountsTable = $this->getAccountsTableName($currentSY);

		$sql = "
		UPDATE
		  {$accountsTable} a1
		  INNER JOIN
		    (SELECT
		      b1.`account_id`,
		      b1.`due_amount`
		    FROM
		      billings b1
		      INNER JOIN
		        (SELECT
		          account_id,
		          MAX(id) AS latest_billing_id
		        FROM
		          billings
		        WHERE due_date = '{$closingDate}'
		          AND sy = {$currentSY}
		        GROUP BY account_id) AS b2
		        ON (b1.`id` = b2.latest_billing_id)
		    ORDER BY b1.due_amount DESC) b3
		    ON (b3.account_id = a1.id)
		SET a1.`outstanding_balance` = b3.due_amount;
		";

		$this->query($sql);

		// Get count of updated records
		$countResult = $this->query("SELECT ROW_COUNT() as updated_count");
		return $countResult[0][0]['updated_count'];
	}

	/**
	 * Copy accounts to next year table with reset values
	 *
	 * @param int $currentSY Current school year
	 * @param int $nextSY Next school year
	 */
	protected function copyAccountsToNextYear($currentSY, $nextSY) {
		// Use helper function to get correct table names
		$sourceAccountsTable = $this->getAccountsTableName($currentSY);
		$targetAccountsTable = $this->getAccountsTableName($nextSY);

		// First truncate the next year table
		$this->query("TRUNCATE TABLE {$targetAccountsTable}");

		$sql = "
		INSERT INTO {$targetAccountsTable} (
		    id,
		    account_type,
		    sy,
		    ref_no,
		    account_details,
		    payment_scheme,
		    assessment_total,
		    subsidy_status,
		    discount_amount,
		    payment_total,
		    old_balance,
		    outstanding_balance,
		    module_balance,
		    rounding_off,
		    created,
		    modified
		)
		SELECT
		    id,
		    account_type,
		    {$nextSY} AS sy,
		    '' AS ref_no,
		    'INIT_ACCOUNT' AS account_details,
		    NULL AS payment_scheme,
		    0.00 AS assessment_total,
		    subsidy_status,
		    0.00 AS discount_amount,
		    0.00 AS payment_total,
		    0.00 AS old_balance,
		    0.00 AS outstanding_balance,
		    0.00 AS module_balance,
		    0.00 AS rounding_off,
		    NOW() AS created,
		    NOW() AS modified
		FROM {$sourceAccountsTable};
		";

		$this->query($sql);
	}

	/**
	 * Migrate outstanding balances from current year to old_balance in next year
	 *
	 * @param int $currentSY Current school year
	 * @param int $nextSY Next school year
	 * @return int Number of balances migrated
	 */
	protected function migrateOutstandingBalances($currentSY, $nextSY) {
		// Use helper function to get correct table names
		$sourceAccountsTable = $this->getAccountsTableName($currentSY);
		$targetAccountsTable = $this->getAccountsTableName($nextSY);

		$sql = "
		UPDATE {$targetAccountsTable} a25
		INNER JOIN {$sourceAccountsTable} a24 ON (a24.id = a25.id)
		SET a25.`old_balance` = a24.`outstanding_balance`
		WHERE a24.`outstanding_balance` != 0;
		";

		$this->query($sql);

		// Get count of updated records
		$countResult = $this->query("SELECT ROW_COUNT() as migrated_count");
		return $countResult[0][0]['migrated_count'];
	}

	/**
	 * Get summary of accounts with outstanding balances for a specific closing date
	 *
	 * @param int $sy School year
	 * @param string $closingDate Closing date
	 * @return array Summary data
	 */
	function getOutstandingBalancesSummary($sy, $closingDate) {
		$sql = "
		SELECT
		  COUNT(*) as total_accounts,
		  COUNT(CASE WHEN b3.due_amount > 0 THEN 1 END) as accounts_with_balance,
		  COUNT(CASE WHEN b3.due_amount = 0 THEN 1 END) as fully_paid_accounts,
		  SUM(b3.due_amount) as total_outstanding,
		  AVG(b3.due_amount) as average_balance
		FROM
		  (SELECT
		    b1.`account_id`,
		    b1.`due_amount`
		  FROM
		    billings b1
		    INNER JOIN
		      (SELECT
		        account_id,
		        MAX(id) AS latest_billing_id
		      FROM
		        billings
		      WHERE due_date = '{$closingDate}'
		        AND sy = {$sy}
		      GROUP BY account_id) AS b2
		      ON (b1.`id` = b2.latest_billing_id)) b3;
		";

		$result = $this->query($sql);
		return $result[0][0];
	}

	/**
	 * Helper function to determine the correct accounts table name based on school year
	 *
	 * @param int $sy School year
	 * @return string Table name
	 */
	protected function getAccountsTableName($sy) {
		return ($sy == 2024) ? 'accounts' : "accounts_{$sy}";
	}

	/**
	 * Helper function to determine the correct account_fees table name based on school year
	 *
	 * @param int $sy School year
	 * @return string Table name
	 */
	protected function getAccountFeesTableName($sy) {
		return ($sy == 2024) ? 'account_fees' : "account_fees_{$sy}";
	}

	/**
	 * Helper function to determine the correct account_schedules table name based on school year
	 *
	 * @param int $sy School year
	 * @return string Table name
	 */
	protected function getAccountSchedulesTableName($sy) {
		return ($sy == 2024) ? 'account_schedules' : "account_schedules_{$sy}";
	}

	/**
	 * Get billing cutoff dates for a specific school year
	 *
	 * @param int $sy School year
	 * @return array Billing cutoff data
	 */
	function getBillingCutoffs($sy) {
		$sql = "SELECT * FROM billing_cutoffs WHERE sy = {$sy}";
		$result = $this->query($sql);
		return !empty($result) ? $result[0]['billing_cutoffs'] : null;
	}

	/**
	 * Update master_configs table for school year transitions
	 *
	 * @param string $configKey Configuration key (ACTIVE_SY, MOD_ESP)
	 * @param string $configValue New value
	 * @return bool Success status
	 */
	function updateMasterConfig($configKey, $configValue) {
		$sql = "
		UPDATE master_configs
		SET sys_value = '{$configValue}', modified = NOW()
		WHERE sys_key = '{$configKey}'
		";

		$this->query($sql);
		return true;
	}

	/**
	 * Get current master config value
	 *
	 * @param string $configKey Configuration key
	 * @return string Configuration value
	 */
	function getMasterConfig($configKey) {
		$sql = "SELECT sys_value FROM master_configs WHERE sys_key = '{$configKey}'";
		$result = $this->query($sql);
		return !empty($result) ? $result[0]['master_configs']['sys_value'] : null;
	}

	/**
	 * Handle master_configs updates for school year transitions
	 *
	 * @param int $currentSY Current school year
	 * @param int $nextSY Next school year
	 * @param string $action Action type: 'opening' or 'closing'
	 * @return array Update results
	 */
	function updateSchoolYearConfigs($currentSY, $nextSY, $action) {
		$updates = array();

		if ($action === 'opening') {
			// Opening date of next SY: MOD_ESP = 1 (enable overlapping period)
			$this->updateMasterConfig('MOD_ESP', '1');
			$updates[] = "Set MOD_ESP = 1 (overlapping period enabled)";

		} elseif ($action === 'closing') {
			// Closing date of current SY: MOD_ESP = 0, ACTIVE_SY = nextSY
			$this->updateMasterConfig('MOD_ESP', '0');
			$this->updateMasterConfig('ACTIVE_SY', $nextSY);
			$updates[] = "Set MOD_ESP = 0 (overlapping period disabled)";
			$updates[] = "Set ACTIVE_SY = {$nextSY} (new active school year)";
		}

		return $updates;
	}

	/**
	 * Quick migration function for standard school year transitions
	 * This is a simplified wrapper for common migrations that uses billing_cutoffs data
	 *
	 * @param int $fromSY Source school year
	 * @param int $toSY Target school year
	 * @param string $migrationDate Migration date (format: YYYY-MM-DD). If null, uses closing_date from billing_cutoffs
	 * @return array Migration result
	 */
	function quickMigrate($fromSY, $toSY, $migrationDate = null, $forceOverride = false) {
		// Get billing cutoffs for the source school year
		$cutoffs = $this->getBillingCutoffs($fromSY);

		if (!$migrationDate) {
			if ($cutoffs) {
				// Use closing_date as migration trigger
				$migrationDate = $cutoffs['closing_date'];
				echo "Using closing_date from billing_cutoffs: {$migrationDate}\n";
			} else {
				// Fallback to default pattern if no cutoffs found
				$migrationDate = ($fromSY + 1) . '-05-23';
				echo "No billing_cutoffs found, using default: {$migrationDate}\n";
			}
		}

		// For outstanding balance calculation, use end_bill_date
		$endBillDate = $cutoffs ? $cutoffs['end_bill_date'] : ($fromSY + 1) . '-04-07';

		echo "Quick Migration: SY {$fromSY} to SY {$toSY}\n";
		echo "Migration Date (closing_date): {$migrationDate}\n";
		echo "End Bill Date (for balance calc): {$endBillDate}\n";
		echo "Source Table: " . $this->getAccountsTableName($fromSY) . "\n";
		echo "Target Table: " . $this->getAccountsTableName($toSY) . "\n";

		// Check migration timing first
		$validation = $this->validateMigrationTiming($fromSY, $forceOverride);
		echo "Current Date: " . $validation['current_date'] . "\n";
		echo "Current Period: " . $validation['current_period'] . "\n";
		echo "Migration Status: " . $validation['status'] . "\n";

		if (!empty($validation['warnings'])) {
			echo "⚠️  WARNINGS:\n";
			foreach ($validation['warnings'] as $warning) {
				echo "   - {$warning}\n";
			}
		}

		if (!$validation['allowed'] && !$forceOverride) {
			echo "❌ MIGRATION BLOCKED: " . $validation['message'] . "\n";
			if (!empty($validation['recommendations'])) {
				echo "📋 RECOMMENDATIONS:\n";
				foreach ($validation['recommendations'] as $rec) {
					echo "   - {$rec}\n";
				}
			}
			echo "Use forceOverride=true to bypass validation.\n\n";
		} else {
			if ($forceOverride && !$validation['allowed']) {
				echo "⚠️  OVERRIDE: Proceeding despite validation warnings\n";
			}
			echo "✅ Proceeding with migration...\n";
		}
		echo "\n";

		// Use end_bill_date for outstanding balance calculation
		return $this->initializeNextSchoolYear($fromSY, $toSY, $endBillDate, $forceOverride);
	}

}
