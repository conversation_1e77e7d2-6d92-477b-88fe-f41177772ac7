<?php

 // Define constants needed for CakePHP paths
 if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}
if (!defined('ROOT')) {
    // Set the absolute path to the application root
    define('ROOT',dirname(dirname(dirname(dirname(__FILE__)))));
}
if (!defined('APP_DIR')) {
    define('APP_DIR', 'api');
}
if (!defined('APP')) {
    define('APP', ROOT . DS . APP_DIR . DS);
}

if(!defined('SESSION_KEY')){
	define('SESSION_KEY', 'CAHA_PAY');
}

// Define session path
if(!defined('SESSION_PATH')){
	define('SESSION_PATH', APP . 'tmp' . DS . 'sessions');
}

// Create session directory if it doesn't exist
if (!is_dir(SESSION_PATH)) {
    @mkdir(SESSION_PATH, 0777, true);
}
