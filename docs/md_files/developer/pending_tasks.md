# Pending Tasks and Development Priorities

## Overview
This document outlines the current high priority tasks, pending work items, and development priorities for the Caha Pay system. Tasks are organized by priority level and include detailed implementation guidance.

## High Priority Tasks

### 1. Database Configuration Migration
**Status**: Pending
**Priority**: High
**Estimated Effort**: 2-3 days

#### Current Issue
Billing constants are currently hardcoded in `utils/billings.js`, making configuration changes require code modifications.

#### Current Implementation
```javascript
// TODO: These constants should be stored in the database and retrieved via API
// This will allow for easier configuration changes without code modifications
BILLING.start = 8; // September (0-based index, 8 = Sep)
BILLING.month_due_date = 7; // 7th of the month
BILLING.last_bill_date = '07 APR 2025'; // Format: DD MMM YYYY
```

#### Required Changes
1. **Database Schema**: Create a `system_config` table to store billing configuration
2. **API Endpoint**: Create endpoint to retrieve billing configuration
3. **Frontend Update**: Modify `utils/billings.js` to fetch configuration from API
4. **Admin Interface**: Create interface for updating billing configuration

#### Implementation Steps
1. Create database migration for `system_config` table
2. Add configuration seeding with current values
3. Create API controller method for configuration retrieval
4. Update `utils/billings.js` to use API data
5. Create admin interface for configuration management
6. Test configuration changes across all modules

#### Files to Modify
- `api/sql/` - New migration file
- `api/controllers/` - New configuration controller
- `utils/billings.js` - Replace hardcoded values with API calls
- Admin interface files (TBD)

### 2. Assessment Functionality Integration Planning
**Status**: On Hold (Intentional)
**Priority**: High (for planning)
**Estimated Effort**: 1 week planning + 2-3 weeks implementation

#### Current Status
Assessment functionality is intentionally disabled for SY 2025 implementation per project requirements.

#### Planning Requirements
1. **Architecture Review**: Design assessment data structure for SY 2025
2. **Integration Points**: Identify where assessment data connects with payment processing
3. **Data Migration**: Plan for existing assessment data handling
4. **Testing Strategy**: Develop comprehensive testing approach

#### When Ready to Implement
- Assessment data should be included in INIPY/FULPY payment processing
- Integration with Statement of Account generation
- Compatibility with existing payment schedules

### 3. Payment Processing Enhancement
**Status**: Partially Complete
**Priority**: High
**Estimated Effort**: 1-2 days

#### Current Implementation
Payment processing for SY 2025 includes basic fee structure but needs assessment field integration.

#### Required Enhancements
1. **Assessment Field Addition**: Ensure assessment data is properly included in payment objects
2. **Precomputed Values**: Add precomputed fees and schedules similar to SOA
3. **Validation**: Enhance validation for SY 2025 payment structures

#### Implementation Notes
- Focus on INIPY (Initial Payment) and FULPY (Full Payment) transaction types
- Maintain backward compatibility with existing payment processing
- Include comprehensive error handling

## Medium Priority Tasks

### 4. Comprehensive Testing Suite
**Status**: Pending
**Priority**: Medium
**Estimated Effort**: 1 week

#### Testing Areas
1. **SY 2025 Payment Structures**: Validate all fee calculations
2. **SOA Generation**: Test with new fee structures
3. **Grade Level Progression**: Verify student advancement logic
4. **Payment Schedule Mapping**: Ensure correct schedule assignments

### 5. Code Cleanup and Optimization
**Status**: Ongoing
**Priority**: Medium
**Estimated Effort**: 2-3 days

#### Cleanup Tasks
1. **Fee Processing Scripts**: Review and consolidate Python scripts in `lib/fees/`
2. **Temporary Files**: Remove or archive temporary documentation files
3. **Code Documentation**: Update inline documentation for recent changes

## Low Priority Tasks

### 6. Future School Year Automation
**Status**: Planning
**Priority**: Low
**Estimated Effort**: 1-2 weeks

#### Automation Goals
1. **Generalized Approach**: Develop framework for future school years
2. **Table Creation**: Automate creation of year-specific database tables
3. **Student Migration**: Create tools for promoting students between school years

## Implementation Guidelines

### Before Starting Any Task
1. **Review Current State**: Ensure understanding of existing implementation
2. **Create Branch**: Use feature branches for all development work
3. **Update Documentation**: Document changes as they are implemented
4. **Test Thoroughly**: Include both unit and integration testing

### Code Standards
- Follow existing CakePHP 1.3 and AngularJS patterns
- Use `"use strict";` in all JavaScript files
- Implement proper error handling
- Include comprehensive comments for complex logic

### Testing Requirements
- Test all changes with both SY 2024 and SY 2025 data
- Verify backward compatibility
- Include edge case testing
- Document test procedures

## Progress Tracking

### Completed Recently
- ✅ SY 2025 fee structure implementation
- ✅ SOA pagination fixes
- ✅ Payment policy file creation
- ✅ Database table preparation

### In Progress
- 🔄 Documentation updates
- 🔄 Code cleanup activities

### Blocked/On Hold
- ⏸️ Assessment functionality (intentional hold)
- ⏸️ Future school year automation (low priority)

## Notes for Developers

### Important Reminders
- Do not implement assessment functionality for SY 2025 until explicitly approved
- Always test changes with both current and previous school year data
- Maintain backward compatibility with existing functionality
- Follow the established documentation guidelines for all updates

### Contact Information
For questions about these tasks or implementation details, refer to the project documentation or consult with the development team lead.

---
*Last Updated: Based on git history review through May 29, 2025*
