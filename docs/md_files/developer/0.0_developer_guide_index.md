# Developer Guide Index

This index provides links to all developer documentation for the system.

## Core Modules

* [Cashier Module](cashier_module.md) - Detailed documentation of the cashier module, including school year transition handling
* [Statement of Account](statement_of_account.md) - Documentation of the SOA generation process and layout management
* [Billing Cutoffs](billing_cutoffs.md) - Information about billing periods and cutoff dates
* [SY 2025 Implementation](sy_2025_implementation.md) - Details about the School Year 2025 implementation

## Project Management

* [Pending Tasks](pending_tasks.md) - High priority tasks and TODOs for the development team

## Architecture

* Coming soon - System architecture overview

## API Documentation

* Coming soon - API endpoints and usage

## Database Schema

* Coming soon - Database structure and relationships

## Development Guidelines

* Coming soon - Coding standards and best practices
