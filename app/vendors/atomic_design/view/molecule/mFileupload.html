<label class="thumbnail btn btn-default ng-cloak" ng-disabled="FileUploadStarted || FileDisabled"  style="cursor:pointer;" for="{{'fileInput-'+ELEM_ID}}">
	<div ng-show="FileType=='image'">
		<img src="" alt="" class="FilePreview img-responsive"/>
	</div>
	<div class="caption" ng-show="!FileModel">
	  <center>
	  	
	  	<span ng-if="!FileDisabled">
			<a-glyph icon="folder-open"></a-glyph>
			&nbsp; Browse {{Caption}}</span>
		<span ng-if="FileDisabled">
			<a-glyph icon="ban-circle"></a-glyph>
			 No need to upload {{Caption}}</span>
		</center>
	</div>
	<div ng-show="FileType=='excel'">
		<span ng-show="ReadingFile">Reading file...</span>
		<span ng-show="FileModel">
			<a-glyph icon="file-excel-o"></a-glyph>
			File: {{FileModel.filename}}
		</span>
	</div>
	<div ng-show="FileType=='pdf'">
		<span ng-show="ReadingFile">Reading pdf...</span>
		<span ng-show="FileModel">
			<a-glyph icon="open-file" ng-show="!FileModel.success"></a-glyph> 
			<a-glyph icon="ok" ng-show="FileModel.success"></a-glyph> 
			<span ng-show="FileUploadStarted">Uploading </span>
			File: {{FileModel.filename}}
		</span>
	</div>
</label>
<input class="form-control hide" id="{{'fileInput-'+ELEM_ID}}" type="file"  accept="{{FileTypes}}"/ ng-required="FileRequired && !FileModel" name="{{FileInpuName}}" ng-model="FileInput">
